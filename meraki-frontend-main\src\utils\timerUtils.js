/**
 * Shared Timer Utilities for Real-time Task Synchronization
 * Used by both React Web App and Electron Desktop App
 */

/**
 * Calculate elapsed time from start time to now
 * @param {number} startTime - Unix timestamp when timer started
 * @returns {number} Elapsed seconds
 */
export const calculateElapsedTime = (startTime) => {
  if (!startTime) { return 0 }
  return Math.floor((Date.now() - startTime) / 1000);
};

/**
 * Format seconds to HH:MM:SS display
 * @param {number} seconds - Total seconds
 * @returns {string} Formatted time string
 */
export const formatTime = (seconds) => {
  if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
    return "00:00:00";
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Create timer state object
 * @param {string} taskId - Task identifier
 * @param {string} userId - User identifier  
 * @param {string} projectId - Project identifier
 * @param {number} startTime - Unix timestamp when started
 * @param {boolean} isPaused - Whether timer is paused
 * @param {number} elapsedBeforePause - Seconds elapsed before pause
 * @returns {Object} Timer state object
 */
export const createTimerState = (taskId, userId, projectId, startTime, isPaused = false, elapsedBeforePause = 0) => {
  return {
    taskId,
    userId,
    projectId,
    startTime,
    isPaused,
    elapsedBeforePause,
    currentElapsed: isPaused ? elapsedBeforePause : calculateElapsedTime(startTime) + elapsedBeforePause
  };
};

/**
 * Get current elapsed time for a timer state
 * @param {Object} timerState - Timer state object
 * @returns {number} Current elapsed seconds
 */
export const getCurrentElapsed = (timerState) => {
  if (!timerState) { return 0 }
  if (timerState.isPaused) { return timerState.elapsedBeforePause }
  return calculateElapsedTime(timerState.startTime) + (timerState.elapsedBeforePause || 0);
};

/**
 * Check if timer is owned by current user
 * @param {Object} timerState - Timer state object
 * @param {string} currentUserId - Current user ID
 * @returns {boolean} Whether current user owns the timer
 */
export const isTimerOwner = (timerState, currentUserId) => {
  return timerState && timerState.userId === currentUserId;
};

/**
 * Check if timer is running (not paused and has start time)
 * @param {Object} timerState - Timer state object
 * @returns {boolean} Whether timer is actively running
 */
export const isTimerRunning = (timerState) => {
  return timerState && timerState.startTime && !timerState.isPaused;
};

// Global timer state management
let globalTimerState = null;

/**
 * Get global timer state
 * @returns {Object|null} Current global timer state
 */
export const getGlobalTimerState = () => {
  return globalTimerState || null;
};

/**
 * Get running task from global timer state
 * @returns {Object|null} Running task or null
 */
export const getRunningTask = () => {
  return globalTimerState?.runningTask || null;
};

/**
 * Check if global timer has running task
 * @returns {boolean} Whether there's a running task
 */
export const hasRunningTask = () => {
  return Boolean(globalTimerState?.runningTask);
};

/**
 * Set global timer state
 * @param {Object|null} timerState - Timer state to set globally
 */
export const setGlobalTimerState = (timerState) => {
  globalTimerState = timerState;
};

/**
 * Clear global timer state
 */
export const clearGlobalTimerState = () => {
  globalTimerState = null;
};

/**
 * Format decimal hours to time display (legacy function)
 * @param {number} decimalHours - Decimal hours (e.g., 1.5 = 1h 30m)
 * @returns {string} Formatted time string
 */
export const formatDecimalToTime = (decimalHours) => {
  if (typeof decimalHours !== 'number' || isNaN(decimalHours) || decimalHours < 0) {
    return "0h 0m";
  }
  
  const hours = Math.floor(decimalHours);
  const minutes = Math.floor((decimalHours - hours) * 60);
  
  return `${hours}h ${minutes}m`;
};