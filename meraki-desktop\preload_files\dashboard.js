const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Validate ipc<PERSON>ender<PERSON> is available
if (!ipc<PERSON><PERSON><PERSON>) {
  throw new Error('ipc<PERSON>enderer is not available');
}

// Safely expose APIs to the renderer
try {
  contextBridge.exposeInMainWorld('electronAPI', {
    loginSuccess: () => ipcRenderer.send('login-success'),
    getUpdatedTasks: () => ipcRenderer.send("get-updated-tasks"),
    getRecentTask: () => ipcRenderer.send("get-recent-tasks"),
    productGetFun: (callback) => ipcRenderer.on("get-product", (_event, data) => callback(data)),
    clearTaskDataFun: (callback) => ipcRenderer.on("clear-task", () => callback()),
    getUpdateProgressFun: (callback) => ipcRenderer.on("update-progress", (_event, data) => callback(data)),
    getStoreValue: (key) => ipc<PERSON>enderer.invoke('store:get', key),
    setStoreValue: (key, value) => ipcRenderer.invoke('store:set', key, value),
    startSocketFuncationlity: (callback) => ipcRenderer.on("socket-init", (_event, data) => callback(data)),
    socketCheckOut: () => ipcRenderer.send("socket-checkout"),
    socketBreakIn: () => ipcRenderer.send("socket-breakin"),
    socketBreakOut: () => ipcRenderer.send("socket-breakout"),
    socketCheckInEmit: (callback) => {
      // Add null check for callback
      ipcRenderer.on("check-in-emit", () => callback?.())
    },
    socketCheckOutEmit: (callback) => {
      ipcRenderer.on("check-out-emit", () => callback?.())
    },
    socketBreakInEmit: (callback) => {
      ipcRenderer.on("break-in-emit", (_event, data) => callback?.(data))
    },
    socketBreakOutEmit: (callback) => {
      ipcRenderer.on("break-out-emit", (_event, data) => callback?.(data))
    },
    startTask: (data) => ipcRenderer.invoke('start-task', data),
    pauseTask: (data) => ipcRenderer.invoke('pause-task', data),
    stopTask: (data) => ipcRenderer.invoke('stop-task', data)
  });
} catch (error) {
  console.error('Failed to expose electron APIs:', error);
}