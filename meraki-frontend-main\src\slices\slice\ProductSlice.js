import { get } from "lodash";

const { createSlice } = require("@reduxjs/toolkit");

export const ProductSlice = createSlice({
    name:"product",
    initialState: {
        products:[],
        pagination:{},
        product :{},
        ongoingProductTaskToday : {}
    },
    reducers: {
        createProduct : () => {},
        deleteProduct: () => {},
        updateProduct: () => {},
        getProductById: () => {},
        getSuccessfullyProducts: (state,action) => {

            if(action.payload.length === 0) {
                state.pagination = {}
                 state.products = []
            } else {
                state.products = action.payload.data
                state.pagination = action.payload.pagination
            }  
            console.log("State Product ",state.products)
        },
        getSuccessfullyProductById: (state,action) => {
            if(action.payload.length === 0) {
                state.pagination = {}
                 state.product = []
            } else {
                state.product = action.payload.data
                state.pagination = action.payload.pagination
            }  
            console.log("State Product ",state.product,action.payload.data)
        },
        getSuccessfullyOnGoingProductsTasksToday: (state,action) => {
            state.ongoingProductTaskToday = action.payload.data
        },
        getProducts: () => {},
        createTaskByAdmin: () => {},
        createProductsTaskByUser: () => {},
        getProductsByUser: () => {},
        updateTask: () => {},
        getTasks: () => {},
        startTask: () => {},
        stopTask: () => {},
        pauseTask: () => {},
        deleteTask: () => {},
        restartTask: () => {},
        getOnGoingProductsTasksToday: () => {}
    }
})

export default ProductSlice