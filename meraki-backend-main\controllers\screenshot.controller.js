'use strict';
const Screenshot = require("../models/screenshot.model")
const fs = require('fs')
 
exports.uploadScreenShot = async (req, res) => {
    try {
        const { keyCounts } = req.body
        const user = req.user
        
        if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" })
        }

        // Convert file to base64
        const imageBuffer = fs.readFileSync(req.file.path)
        const base64Image = imageBuffer.toString('base64')
        
        const now = new Date()
        const dateString = now.toISOString().split('T')[0] // YYYY-MM-DD format
        
        const result = await Screenshot.create({
            imageData: base64Image,
            keyCounts: parseInt(keyCounts) || 0,
            userId: user._id,
            captureDate: now,
            dateString: dateString
        })
        
        // Clean up temporary file
        fs.unlinkSync(req.file.path)
        
        return res.status(200).send({ message: "Screenshot uploaded!", id: result._id })
    } catch (error) {
        console.error("Screenshot upload error:", error)
        return res.status(500).json({ error: "Failed to upload screenshot" })
    }
}

exports.getScreenshotsByUserId = async (req, res) => {
    try {
        const { userId } = req.params
        const { date } = req.query
        
        let query = { userId: userId }
        if (date) {
            query.dateString = date
        }
        
        const result = await Screenshot.find(query)
            .sort({ captureDate: -1 })
            .populate('userId', 'name email')
        
        return res.status(200).send(result)
    } catch (error) {
        console.error("Get screenshots error:", error)
        return res.status(500).json({ error: "Failed to fetch screenshots" })
    }
}

exports.getScreenshotsByDate = async (req, res) => {
    try {
        const { date } = req.params
        const user = req.user
        
        const result = await Screenshot.find({
            userId: user._id,
            dateString: date
        }).sort({ captureDate: -1 })
        
        return res.status(200).send(result)
    } catch (error) {
        console.error("Get screenshots by date error:", error)
        return res.status(500).json({ error: "Failed to fetch screenshots" })
    }
}