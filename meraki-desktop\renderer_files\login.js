// const axios = require('axios');

document.addEventListener('DOMContentLoaded', () => {
    const loginBtn = document.getElementById('loginBtn');
  
    loginBtn.addEventListener('click', async (e) => {
      e.preventDefault();
  
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
  
      try {
        const response = await axios.post('http://localhost:10000/api/login', {
          email,
          password
        });
        if (response.data.token) {
            console.log("Login Data Received : ",response.data)
            window.electronAPI.loginSuccess(response.data.token); // ✅ exposed via preload
        } else {
          document.getElementById('message').innerText = 'Login failed.';
        }
      } catch (err) {
        console.error(err);
        document.getElementById('message').innerText = 'Login error.';
      }
    });
  });
  

