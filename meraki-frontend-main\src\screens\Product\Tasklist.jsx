import React, { useEffect, useState } from "react";
import {
  Box, Card, IconButton, Pagination, Table, TableBody, TableCell,
  TableHead, TableRow, Typography, Button 
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { ProductSelector, UserSelector } from "selectors";
import { ProductActions } from "slices/actions";
import { PlayCircle, StopCircle, CheckCircle, PauseCircle } from "@mui/icons-material";
import styled from "@emotion/styled";
import TaskHeader from "./components/TaskHeader";
import TaskFilterUser from "./components/TaskFilterUser";
import "../../CommonStyle/ButtonStyle.css";
import { formatDecimalToTime, setGlobalTimerState } from "../../utils/timerUtils";
import { socket } from "screens/Dashboard/components/Activity";
// import io from 'socket.io-client';

const FilterBox = styled(Box)(() => ({
  width: "100%",
  marginTop: 30,
  marginBottom: 20,
  display: "flex",
  justifyContent: "space-between",
}));

// Simple timer formatting
const formatTimer = (seconds) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
};

// Socket connection
// let socket = null;
// const initSocket = () => {
//   if (!socket) {
//     socket = io('http://localhost:10000', {
//       transports: ['websocket'],
//       reconnection: true
//     });
//     socket.on('connect', () => console.log('🔌 Web connected'));
//   }
//   return socket;
// };

function Tasklist() {
  const dispatch = useDispatch();
  const products = useSelector(ProductSelector.getProducts()) || [];
  const profile = useSelector(UserSelector.profile()) || {};

  // Simple state
  const [filteredData, setFilteredData] = useState([]);
  const [filter, setFilter] = useState({ page: 1 });
  const [activeTab, setActiveTab] = useState("Today");
  const [runningTaskId, setRunningTaskId] = useState(null);
  const [timerSeconds, setTimerSeconds] = useState(0);
  const [isRunning, setIsRunning] = useState(false);

  // Initialize socket
  useEffect(() => {
    // Receive timer updates from desktop (only if not from current user to avoid conflicts)
    socket.on('timer-update', (data) => {
      if (data && data.userId !== profile._id) {
        // Only update display for other users' timers
        setRunningTaskId(data.taskId);
        setTimerSeconds(data.seconds);
        setGlobalTimerState(data);
      } else if (!data) {
        // Reset when no timer is running
        setRunningTaskId(null);
        setTimerSeconds(0);
        setIsRunning(false);
        setGlobalTimerState(null);
      }
    });

    // Handle button state updates only for current user
    socket.on('button-state-update', (data) => {
      if (data.userId === profile._id) {
        setRunningTaskId(data.taskId || null);
        setIsRunning(data.isRunning || false);
        if (data.seconds !== undefined) {
          setTimerSeconds(data.seconds);
        }
      }
    });

    // Handle task events from desktop
    socket.on('task-started', (data) => {
      if (data.userId === profile._id) {
        // Update state directly to avoid recursive calls
        setRunningTaskId(data.taskId);
        setTimerSeconds(data.seconds || 0);
        setIsRunning(true);
        setGlobalTimerState({
          taskId: data.taskId,
          seconds: data.seconds || 0,
          isRunning: true,
          userId: profile._id
        });
        dispatch(ProductActions.getProductsByUser({ id: profile._id }));
      }
    });

    socket.on('task-paused', (data) => {
      if (data.userId === profile._id) {
        setIsRunning(false);
        setTimerSeconds(0);
        setGlobalTimerState({
          taskId: data.taskId,
          seconds: 0,
          isRunning: false,
          userId: profile._id
        });
        dispatch(ProductActions.getProductsByUser({ id: profile._id }));
      }
    });

    socket.on('task-stopped', (data) => {
      if (data.userId === profile._id) {
        setRunningTaskId(null);
        setIsRunning(false);
        setTimerSeconds(0);
        setGlobalTimerState(null);
        dispatch(ProductActions.getProductsByUser({ id: profile._id }));
      }
    });

    return () => {
      socket.off('timer-update');
      socket.off('button-state-update');
      socket.off('task-started');
      socket.off('task-paused');
      socket.off('task-stopped');
    };
  }, [profile._id]);

  // Single timer tick effect
  useEffect(() => {
    let interval = null;
    if (isRunning && runningTaskId) {
      interval = setInterval(() => {
        setTimerSeconds(prev => {
          const newSeconds = prev + 1;
          // Emit to desktop every 5 seconds to reduce network traffic
          if (newSeconds % 5 === 0 && socket) {
            socket.emit('timer-sync', {
              taskId: runningTaskId,
              seconds: newSeconds,
              isRunning: true,
              userId: profile._id
            });
          }
          return newSeconds;
        });
      }, 1000);
    }
    return () => {
      if (interval) { clearInterval(interval) }
    };
  }, [isRunning, runningTaskId, profile._id]);


  // Fetch products
  useEffect(() => {
    if (profile?._id) {
      dispatch(ProductActions.getProductsByUser({ id: profile._id }));
    }
  }, [profile, dispatch]);

  // Filter tasks
  const filterTasks = (tab, filterConditions) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
const filtered = products.map((product) => {
  const filteredTasks = product.taskArr.filter((task) => {
    const hasAccess = task.assignee.includes(profile._id) || 
                      product.visibility || 
                      product.members.includes(profile._id) ||
                      task.reporter === profile._id;

    if (!hasAccess) { return false }

    const taskCreateDate = new Date(task.createdAt).setHours(0, 0, 0, 0);
    const todayTime = today.getTime();
    const isCompleted = task.taskStatus === "Completed";
    const isRestarted = task.taskStatus === "In Progress" && task.previousStatus === "Completed";

    let tabFilter = true;
    if (tab === "Today") {
      tabFilter = taskCreateDate === todayTime && !isCompleted;
    } else if (tab === "Overdue") {
      tabFilter = (taskCreateDate < todayTime || isRestarted) && !isCompleted;
    } else if (tab === "Upcoming") {
      tabFilter = taskCreateDate > todayTime && !isCompleted;
    } else if (tab === "Completed") {
      tabFilter = isCompleted;
    }

    const userFilter = filterConditions.user ? task.assignee.includes(filterConditions.user) : true;
    const statusFilter = filterConditions.status ? task.taskStatus === filterConditions.status : true;
    const projectFilter = filterConditions.project ? product._id === filterConditions.project : true;

    return tabFilter && userFilter && statusFilter && projectFilter;
  });

  // ✅ Sort tasks by createdAt (descending = newest first)
  const sortedTasks = filteredTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  return { ...product, taskArr: sortedTasks };
}).filter(product => product.taskArr.length > 0);

    setFilteredData(filtered);
  };

  useEffect(() => {
    if (products.length > 0) {
      filterTasks(activeTab, filter);
    }
  }, [products, activeTab, filter]);

  // Task handlers
  const handleStartTask = async (taskId, projectId) => {
    try {
      if (runningTaskId && runningTaskId !== taskId && isRunning) {
        alert("Stop current task first!");
        return;
      }

      const today = new Date().toISOString().split("T")[0];
      dispatch(ProductActions.startTask({ taskId, projectId, date: today }));

      // Start timer
      setRunningTaskId(taskId);
      setTimerSeconds(0);
      setIsRunning(true);

      const timerData = {
        taskId,
        seconds: 0,
        isRunning: true,
        userId: profile._id
      };

      setGlobalTimerState(timerData);

      // Emit to desktop immediately
      if (socket) {
        socket.emit('timer-sync', timerData);
        socket.emit('button-state-update', timerData);
      }

      dispatch(ProductActions.getProductsByUser({ id: profile._id }));

    } catch (error) {
      console.error("❌ Web start error:", error);
    }
  };

  const handlePauseTask = async (taskId, projectId) => {
    try {
      if (runningTaskId !== taskId) { return }

      const today = new Date().toISOString().split("T")[0];
      dispatch(ProductActions.pauseTask({ 
        taskId, 
        projectId, 
        elapsedTime: timerSeconds,
        pauseTime: new Date().toISOString(),
        date: today,
        startTime: new Date().toISOString()
      }));

      // Pause timer - reset to 00:00:00
      setIsRunning(false);
      setTimerSeconds(0);

      const timerData = {
        taskId,
        seconds: 0,
        isRunning: false,
        userId: profile._id
      };

      setGlobalTimerState(timerData);

      // Emit to desktop immediately
      if (socket) {
        socket.emit('timer-sync', timerData);
        socket.emit('button-state-update', timerData);
      }

      dispatch(ProductActions.getProductsByUser({ id: profile._id }));

    } catch (error) {
      console.error("❌ Web pause error:", error);
    }
  };

  const handleStopTask = async (taskId, projectId) => {
    try {
      const today = new Date().toISOString().split("T")[0];
      dispatch(ProductActions.stopTask({ 
        taskId, 
        projectId, 
        elapsedTime: timerSeconds,
        date: today
      }));

      // Stop timer
      setRunningTaskId(null);
      setTimerSeconds(0);
      setIsRunning(false);
      setGlobalTimerState(null);

      // Emit to desktop immediately
      if (socket) {
        socket.emit('timer-sync', null);
        socket.emit('button-state-update', {
          taskId: null,
          isRunning: false,
          seconds: 0,
          userId: profile._id
        });
      }

      dispatch(ProductActions.getProductsByUser({ id: profile._id }));

    } catch (error) {
      console.error("❌ Web stop error:", error);
    }
  };

  const handleRestartTask = async (taskId, projectId) => {
    try {
      if (runningTaskId && runningTaskId !== taskId && isRunning) {
        alert("Please stop the running task first.");
        return;
      }

      const today = new Date().toISOString().split("T")[0];

      dispatch(ProductActions.restartTask({
        taskId,
        projectId,
        date: today
      }));

      setRunningTaskId(taskId);
      setTimerSeconds(0);
      setIsRunning(true);

      const timerData = {
        taskId,
        seconds: 0,
        isRunning: true,
        userId: profile._id
      };
    
      setGlobalTimerState(timerData);

      // Emit to desktop immediately
      if (socket) {
        socket.emit('timer-sync', timerData);
        socket.emit('button-state-update', timerData);
      }

      dispatch(ProductActions.getProductsByUser({ id: profile._id }));

    } catch (error) {
      console.error("❌ Web restart error:", error);
    }
  }

  

  return (
    <div style={{ display: "flex", justifyContent: "center", flexDirection: "column", gap: "5px" }}>
      <Typography variant="h5" sx={{ fontWeight: 600 }}>My Tasks</Typography>

      <Card style={{ padding: "10px" }}>
        <TaskHeader />
        {profile?.role && !profile.role.includes("admin") && (
          <TaskFilterUser projects={products} onFilter={setFilter} />
        )}
      </Card>

      {/* Tab Navigation */}
      <div style={{ display: "flex", gap: "4px" }}>
        {["Today", "Overdue", "Upcoming", "Completed"].map((tab) => (
          <Button
            key={tab}
            onClick={() => {
              setActiveTab(tab);
              filterTasks(tab, filter);
            }}
            sx={{
              borderRadius: 0,
              boxShadow: "none",
              textTransform: "none",
              fontSize:"16px",
              fontWeight:"Bold",
              borderBottom: activeTab === tab ? "3px solid rgb(111, 0, 255)" : "3px solid transparent",
              color: activeTab === tab ? "#000" : "#757575",
            }}
          >
            {tab}
          </Button>
        ))}
      </div>

      {/* Task Table */}
      <Card style={{ padding: "10px" }}>
        <Box>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="center">Task Name</TableCell>
                <TableCell align="center">Project Name</TableCell>
                <TableCell align="center">Actions</TableCell>
                <TableCell align="center">Timer</TableCell>
                <TableCell align="center">Total Spent</TableCell>
                <TableCell align="center">Total Hours</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredData.length > 0 ? (
                filteredData.map((data) =>
                  data.taskArr.map((task) => {
                    if (task.assignee.includes(profile._id) || data.visibility || task.reporter === profile._id) {
                      const isCurrentTask = runningTaskId === task._id;
                      const showPause = isCurrentTask && isRunning;
                      const showPlay = !runningTaskId || (isCurrentTask && !isRunning);
                      
                      return (
                        <TableRow key={task._id + task.taskStatus}>
                          <TableCell align="center">{task.taskTitle}</TableCell>
                          <TableCell align="center">{data.productName}</TableCell>
                          <TableCell align="center">
                  {task.taskStatus === "Completed" ? (
  <IconButton color="success"
    onClick={() => handleRestartTask(task._id, data._id)}
      color2={isCurrentTask && isRunning ? "warning" : "primary"}
  >
    <CheckCircle />
  </IconButton>
) : (
  <>
    {/* Play / Pause Button */}
    <IconButton
      onClick={() => { isCurrentTask && isRunning ? handlePauseTask(task._id, data._id) : handleStartTask(task._id, data._id) }}
      color={isCurrentTask && isRunning ? "warning" : "primary"}
      disabled={isRunning && !isCurrentTask} // ✅ only disable if another task is running
    >
      {isCurrentTask && isRunning ? <PauseCircle /> : <PlayCircle />}
    </IconButton>

    {/* Stop Button */}
    <IconButton
      onClick={() => handleStopTask(task._id, data._id)}
      color="secondary"
      disabled={!isCurrentTask && isRunning }
    >
      <StopCircle />
    </IconButton>
  </>
)}
                          </TableCell>
                          <TableCell align="center">
                            {isCurrentTask ? formatTimer(timerSeconds) : formatDecimalToTime(task.totalSpent)}
                          </TableCell>
                          <TableCell align="center">{formatDecimalToTime(task.totalSpent)}</TableCell>
                          <TableCell align="center">{formatDecimalToTime(task.totalHours)}</TableCell>
                        </TableRow>
                      );
                    }
                    return null;
                  })
                )
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">No Data Found</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <Pagination sx={{ mt: 1 }} page={filter.page} onChange={(_, val) => setFilter({ ...filter, page: val })} />
        </Box>
      </Card>
    </div>
  );
}
export default Tasklist;