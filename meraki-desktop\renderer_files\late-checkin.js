// const axios = require('axios');

document.addEventListener('DOMContentLoaded', () => {
    const lateInBtn = document.getElementById('lateInBtn');
    
    lateInBtn.addEventListener('click', async (e) => {
      
        e.preventDefault();
        const description = document.getElementById('lateCheckIn').value;
        console.log("Late Check In Here : ",description)
      window.electronAPI.lateCheckInFun(description);
    });
});
  

