{"name": "meraki-desktop", "version": "1.0.0", "main": "main.js", "scripts": {"dev": "nodemon --watch main.js --exec \"electron .\"", "start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make --arch=arm64", "rebuild": "electron-rebuild -f -w sharp"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-dmg": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron/fuses": "^1.8.0", "electron": "^36.2.0", "electron-rebuild": "^3.2.9"}, "dependencies": {"axios": "^1.9.0", "dotenv": "^16.5.0", "electron-squirrel-startup": "^1.0.1", "electron-store": "^8.2.0", "form-data": "^4.0.2", "node-global-key-listener": "^0.3.0", "nodemon": "^3.1.10", "sharp": "^0.34.1", "socket.io-client": "^4.8.1"}, "forge": {"packagerConfig": {"asarUnpack": ["**/sharp/**", "**/sharp-libvips-darwin-arm64/**"]}, "plugins": [["@electron-forge/plugin-auto-unpack-natives", {"asar": true}]]}, "config": {"forge": {"makers": [{"name": "@electron-forge/maker-dmg", "config": {"format": "ULFO"}}]}}}