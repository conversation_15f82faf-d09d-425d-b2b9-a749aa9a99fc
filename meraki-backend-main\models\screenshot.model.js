    const mongoose = require("mongoose")
    
    const screeShotSchema = new mongoose.Schema(
        {
            imageData: {
                type: String,
                required: true
            },
            keyCounts: {
                type: Number,
                default: 0
            },
            userId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User',
                required: true
            },
            captureDate: {
                type: Date,
                default: Date.now
            },
            dateString: {
                type: String,
                required: true
            }
        },
        { timestamps: true }
    )

    // Index for efficient queries
    screeShotSchema.index({ userId: 1, dateString: 1 })
    
    const ScreenShotModel = mongoose.model("screenshot", screeShotSchema)
    
    module.exports = ScreenShotModel