import axios from 'axios'

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:10000/api'

const getAuthHeaders = () => {
  const token = localStorage.getItem('merakihr-token')
  return {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
}

export const screenshotApi = {
  // Get screenshots by date for current user
  getScreenshotsByDate: async (date) => {
    try {
      console.log('Fetching screenshots for date:', date)
      console.log('API URL:', `${API_BASE_URL}/track/get-screenshots-date/${date}`)
      const response = await axios.get(
        `${API_BASE_URL}/track/get-screenshots-date/${date}`,
        { headers: getAuthHeaders() }
      )
      console.log('Screenshots response:', response.data)
      return response.data
    } catch (error) {
      console.error('Screenshot API error:', error.response?.data || error.message)
      throw error
    }
  },

  // Get screenshots by user ID with optional date filter
  getScreenshotsByUserId: async (userId, date) => {
    const url = `${API_BASE_URL}/track/get-screenshot-userid/${userId}`
    const params = date ? { date } : {}
    
    const response = await axios.get(url, {
      headers: getAuthHeaders(),
      params
    })
    return response.data
  }
}