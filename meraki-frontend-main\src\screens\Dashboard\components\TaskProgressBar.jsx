import React, { useState, useEffect, useCallback } from "react";
import { Tooltip } from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { TimelineSelector, ProductSelector } from "../../../selectors";
import { TimelineActions } from "../../../slices/actions";
import "../../../App.css";

const TaskProgressBar = () => {
  const dispatch = useDispatch();
  const timelineToday = useSelector(TimelineSelector.getTimelineRequestsToday());
  const ongoingTasks = useSelector(ProductSelector.getOnGoingProductsTasksToday());
  
  const [globalTimerState, setGlobalTimerState] = useState(null);
  const [toolTipTitle, setTooltipTitle] = useState("");
  const [toolTipController, setToolTipController] = useState(false);
  const [hoveredSlot, setHoveredSlot] = useState(null);

  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    dispatch(TimelineActions.getTimelineRequestByDate(today, 'current-user-id'));
  }, [dispatch]);

  useEffect(() => {
    if (timelineToday && Object.keys(timelineToday).length > 0) {
      const currentTask = timelineToday.tasks?.find(task => 
        task.status === 'running' || task.status === 'active'
      );
      
      if (currentTask) {
        const startTime = new Date(currentTask.fromTime || currentTask.startTime);
        const now = new Date();
        const elapsed = Math.floor((now - startTime) / 1000);
        
        setGlobalTimerState({
          startTime: startTime,
          isPaused: currentTask.status === 'paused',
          taskTitle: currentTask.taskDetails?.taskTitle || currentTask.description || 'Current Task',
          projectName: currentTask.taskDetails?.projectName || 'Active Project',
          currentElapsed: elapsed
        });
      } else {
        setGlobalTimerState(null);
      }
    }
  }, [timelineToday]);

  useEffect(() => {
  const interval = setInterval(() => {
    setGlobalTimerState(prev => {
      if (!prev?.startTime) { return prev }
      const now = new Date();
      const elapsed = Math.floor((now - new Date(prev.startTime)) / 1000);
      return {
        ...prev,
        currentElapsed: elapsed
      };
    });
  }, 60000); // Update every 60 seconds (or every second if you prefer)

  return () => clearInterval(interval);
}, []);


const hours = Array.from({ length: 24 }).map((_, i) => {
  if (i === 0) { return "12AM" }
  if (i < 12) { return `${i}AM` }
  if (i === 12) { return "12PM" }
  return `${i - 12}PM`;
});

  const minArr = Array.from({ length: 60 }, (_, i) => i);

  const formatDuration = (seconds) => {
    const totalSeconds = Math.max(0, Math.floor(seconds));
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const getSlotColor = useCallback((hour, minute) => {
    const slotTime = new Date();
    slotTime.setHours(hour, minute, 0, 0);

    if (globalTimerState?.startTime) {
      const startTime = new Date(globalTimerState.startTime);
      const now = new Date();
      if (slotTime >= startTime && slotTime <= now) {
        return globalTimerState.isPaused ? "#FF9800" : "#4CAF50";
      }
    }
    return "#E0E0E0";
  }, [globalTimerState]);

  const handleMouseEnter = useCallback((hour, minute) => {
    const slotTime = new Date();
    slotTime.setHours(hour, minute, 0, 0);
    const slotKey = `${hour}-${minute}`;
    setHoveredSlot(slotKey);

    if (globalTimerState?.startTime) {
      const taskStartTime = new Date(globalTimerState.startTime);
      const now = new Date();

      if (slotTime >= taskStartTime && slotTime <= now) {
        const status = globalTimerState.isPaused ? "Paused" : "Running";
        const tooltipContent = `🔄 LIVE TASK\nTask: ${globalTimerState.taskTitle}\nProject: ${globalTimerState.projectName}\nStatus: ${status}\nTime: ${formatDuration(globalTimerState.currentElapsed)}\nStart: ${taskStartTime.toLocaleTimeString()}`;
        setTooltipTitle(tooltipContent);
        setToolTipController(true);
        return;
      }
    }

    setTooltipTitle("");
    setToolTipController(false);
  }, [globalTimerState, formatDuration]);

  const handleMouseLeave = () => {
    setTooltipTitle("");
    setToolTipController(false);
    setHoveredSlot(null);
  };

  const renderProgressBars = () => {
    const progressBars = [];

    hours.forEach((_, hourIndex) => {
      minArr.forEach((minute) => {
        const activityColor = getSlotColor(hourIndex, minute);
        const slotKey = `${hourIndex}-${minute}`;

        progressBars.push(
          <div
            key={slotKey}
            className="progress-bar-slot"
            style={{
              width: "1.04%",
              height: "100%",
              backgroundColor: activityColor,
              display: "inline-block",
              position: "relative"
            }}
            onMouseEnter={() => handleMouseEnter(hourIndex, minute)}
            onMouseLeave={handleMouseLeave}
          >
            {toolTipController && toolTipTitle && hoveredSlot === slotKey && (
              <Tooltip
                title={<div style={{ whiteSpace: "pre-line" }}>{toolTipTitle}</div>}
                arrow
                open={true}
                placement="top"
              >
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    pointerEvents: "none"
                  }}
                />
              </Tooltip>
            )}
          </div>
        );
      });
    });

    return progressBars;
  };

  return (
    <>
      <div style={{ marginBottom: "1px" }}>
        <div className="progress" style={{ height: "10px" }}>
          {renderProgressBars()}
        </div>
      </div>

      {/* Time Labels */}
      <div className="d-flex justify-content-between">
        {hours.map((label, index) => (
          <li key={index} className="timeSlotLi">
            {label}
          </li>
        ))}
      </div>
    </>
  );
};

export default TaskProgressBar;
