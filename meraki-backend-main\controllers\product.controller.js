'use strict';

const ProductService = require("../services/product.service");

/**
 * @desc Create a new product
 * @route POST /api/product/create
 */
exports.createProduct = async (req, res) => {
    try {
        const result = await ProductService.createProduct(req.body);
        return res.status(200).json(result);
    } catch (error) {
        console.error("Error in createProduct:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Update an existing product by ID
 * @route PATCH /api/product/update/:id
 */
exports.updateProduct = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await ProductService.updateProduct(id, req.body);
        return res.status(200).json(result);
    } catch (error) {
        console.error("Error in updateProduct:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Fetch all products with pagination
 * @route GET /api/product
 */
exports.getProducts = async (req, res) => {
    try {
        console.log("Received Queries:", req.query);
        const result = await ProductService.getProducts(req.query);
        return res.status(200).json(result);
    } catch (error) {
        console.error("Error in getProducts:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Delete a product by ID
 * @route DELETE /api/product/delete/:id
 */
exports.deleteProduct = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await ProductService.deleteProduct(id);
        return res.status(200).json({ 
            message: "Product deleted successfully",
            data: result 
        });
    } catch (error) {
        console.error("Error in deleteProduct:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Delete a task from a product
 * @route DELETE /api/product/:productId/task/:taskId
 */
exports.deleteTask = async (req, res) => {
    try {
        const { productId, taskId } = req.params;
        const result = await ProductService.deleteTask(productId, taskId);
        return res.status(200).json({
            message: "Task deleted successfully",
            data: result
        });
    } catch (error) {
        console.error("Error in deleteTask:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Get product details by ID
 * @route GET /api/product/:id
 */
exports.getProductById = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await ProductService.getProductById(id);
        return res.status(200).json(result);
    } catch (error) {
        console.error("Error in getProductById:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Add a new task to a product
 * @route PATCH /api/product/create/task/:id
 */
exports.createTask = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Validate required fields
        if (!req.body.taskTitle || !req.body.assignee) {
            return res.status(400).json({ 
                error: "Task title and assignee are required" 
            });
        }
        
        const result = await ProductService.createTask(id, req.body);
        return res.status(201).json(result);
    } catch (error) {
        console.error("Error in createTask:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Get all products assigned to a user
 * @route GET /api/product/user/:id
 */
exports.getProductsByUser = async (req, res) => {
    try {
        const { id } = req.params;
        console.log("Fetching products for user ID:", id);
        const result = await ProductService.getProductsByUser(id);
        // console.log("Fetched products:", result);
        return res.status(200).json(result);
    } catch (error) {
        console.error("Error in getProductsByUser:", error);
        return res.status(500).json({ error: error.message });
    }
};


/**
 * @desc Update task details within a product
 * @route PATCH /api/product/update/task/:productid/:taskid
 */
exports.updateTask = async (req, res) => {
    try {
        const { productId, taskId } = req.params;
        console.log("Update task params:", productId, taskId);
        console.log("Update task body:", req.body);
        
        const result = await ProductService.updateTask(productId, taskId, req.body);
        return res.status(200).json(result);
    } catch (error) {
        console.error("Error in updateTask:", error);
        return res.status(500).json({ error: error.message });
    }
};


/**
 * @desc Start a task timer
 * @route PATCH /api/product/start-task/:productId/:taskId
 */
exports.startTask = async (req, res) => {
    try {
        const { productId, taskId } = req.params;
        const { date } = req.body || {};
        
        console.log(`Starting task ${taskId} for date: ${date || 'today'}`);
        
        const task = await ProductService.startTask(productId, taskId, date);
        return res.json({ task, message: "Task started successfully" });
    } catch (error) {
        console.error("Error in startTask:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Restart a completed task
 * @route PATCH /api/product/restart-task
 */
/**
 * @desc Restart a completed task
 * @route PATCH /api/product/restart-task/:productId/:taskId?date=YYYY-MM-DD
 */
exports.restartTask = async (req, res) => {
    try {
        const { productId, taskId } = req.params;
        const { date } = req.query;

        if (!productId || !taskId) {
            return res.status(400).json({ message: "productId and taskId are required." });
        }

        console.log(`Restarting task ${taskId} for date: ${date || 'today'}`);

        const task = await ProductService.restartTask(productId, taskId, date);

        return res.status(200).json({
            task,
            message: "Task restarted successfully"
        });
    } catch (error) {
        console.error("Error in restartTask:", error);
        return res.status(500).json({ error: error.message });
    }
};



/**
 * @desc Stop a task timer and log the hours
 * @route PATCH /api/product/stop-task/:productId/:taskId
 */
exports.stopTask = async (req, res) => {
    try {
        const { productId, taskId } = req.params;
        const { elapsedTime, date } = req.body || {};
        
        console.log(`Stopping task ${taskId} with elapsed time: ${elapsedTime}s, date: ${date}`);
        
        const task = await ProductService.stopTask(productId, taskId, elapsedTime, date);
        return res.json({ task, message: "Task stopped successfully" });
    } catch (error) {
        console.error("Error in stopTask:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Submit/Complete a task (alias for stopTask)
 * @route PATCH /api/product/submit-task/:productId/:taskId
 */
exports.submitTask = async (req, res) => {
    try {
        const { productId, taskId } = req.params;
        const { elapsedTime, date } = req.body || {};
        
        console.log(`Submitting task ${taskId} with elapsed time: ${elapsedTime}s, date: ${date}`);
        
        const task = await ProductService.submitTask(productId, taskId, elapsedTime, date);
        return res.json({ task, message: "Task submitted successfully" });
    } catch (error) {
        console.error("Error in submitTask:", error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * @desc Pause a task timer and log the hours so far
 * @route PATCH /api/product/pause-task/:productId/:taskId
 */
exports.pauseTask = async (req,res) => {
    try {
        const { productId, taskId } = req.params;
        const { elapsedTime, pauseTime, date } = req.body || {};
        
        console.log(`Pausing task ${taskId} with elapsed time: ${elapsedTime}s, pauseTime: ${pauseTime}, date: ${date}`);
        
        const task = await ProductService.pauseTask(productId, taskId, elapsedTime, pauseTime, date);
        return res.json({ task, message: "Task paused successfully" });
    } catch (error) {
        console.error("Error in pauseTask:", error);
        return res.status(500).json({ error: error.message });
    }
}


exports.getOnGoingProductsTasksToday = async (req, res) => {
    try {
        const result = await ProductService.getOnGoingProductsTasksToday();
        return res.status(200).json(result);
    } catch (error) {
        console.error("Error in getOnGoingProductsTasksToday:", error);
        return res.status(500).json({ error: error.message });
    }
};