'use strict';

const { db } = require("../models")
const mongoose = require('mongoose');
const Attendance = db.attendance;

exports.createManyAttendances = async (data) => {
    return Attendance.insertMany(await Promise.all(data));
}

exports.getAttendancesByQuery = async (queries) => {

    const limit = parseInt(queries.limit) || 20;
    const page = parseInt(queries.page) || 1;
    const skip = limit * (page - 1);

    // Handle sort parameter
    let sort = { createdAt: -1 }; // default sort
    if (queries.sort) {
        if (queries.sort === 'newest') {
            sort = { createdAt: -1 };
        } else if (queries.sort === 'oldest') {
            sort = { createdAt: 1 };
        }
    }

    // Build query conditions
    const matchConditions = [];

    // Filter by user if specified
    if (queries.user && queries.user !== '-1') {
        matchConditions.push({
            user: new mongoose.Types.ObjectId(queries.user)
        });
    }

    // console.log("Attendance Query - Limit:", limit, "Page:", page, "Skip:", skip, "Sort:", sort, "User filter:", queries.user, "Match conditions:", matchConditions);

    const aggregate = [];

    // Add match conditions at the beginning for better performance
    if (matchConditions.length > 0) {
        aggregate.push({
            $match: {
                $and: matchConditions
            }
        });
    }

    // Add user lookup
    aggregate.push({
        $lookup: {
            from: 'users',
            let: { user: "$user" },
            pipeline: [
                {
                    $match: {
                        $expr: { $eq: ["$$user", "$_id"] }
                    }
                },
                { $project: { name: 1, department: 1, designation: 1 } }
            ],
            as: 'user'
        }
    });

    aggregate.push({ $unwind: '$user' });

    const results = await Attendance.
    aggregate(aggregate).
    skip(skip).
    limit(limit).
    sort(sort);

    const counts = results.length;
    console.log("Attendance results count:", counts, "Sample data:", results.slice(0, 2));

    return {
        pagination: {
            perPage: limit,
            currentPage: page,
            counts,
            pages: Math.ceil(counts / limit)
        },
        data: results
    }
}

/**
 * Create a new attendance record
 *
 * @param {Object} data - Attendance data including user ID and check-in time
 * @returns {Object} Created attendance record
 * @throws {Error} If user ID is invalid or creation fails
 */
exports.createAttendance = async (data) => {
  try {
    // Validate user ID
    if (!data.user || typeof data.user !== 'string' || data.user.trim() === '') {
      throw new Error('Invalid user ID');
    }

    // Create the attendance record
    return await Attendance.create(data);
  } catch (error) {
    throw error;
  }
};

/**
 * Get attendance record by ID
 *
 * @param {String} id - Attendance record ID
 * @returns {Object} Attendance record
 */
exports.getAttendanceById = async (id) => await Attendance.findById(id);

/**
 * Update attendance record
 *
 * @param {String} id - Attendance record ID
 * @param {Object} data - Updated attendance data
 * @returns {Object} Updated attendance record
 */
exports.updateAttendance = async (id, data) => {
   try {
    console.log("Update Attendance", id, data);
     const attendance = await Attendance.findById(id);
 
    
     if (!attendance) {
       throw new Error('Attendance record not found');
     }

     // Update checkout time
     attendance.checkOut = Date.now();

     // Save and return the updated record
      await attendance.save();
      return attendance;
   } catch (error) {
     throw error;
   }
};

/**
 * Delete attendance record
 *
 * @param {String} id - Attendance record ID
 * @returns {Object} Deletion result
 */
exports.deleteAttendance = async (id) => await Attendance.findByIdAndDelete(id);


/**
 * Get attendance records for a specific month
 *
 * @param {Object} data - Date range data (startDate, endDate)
 * @param {Object} queries - Pagination and sorting options
 * @returns {Object} Attendance records with pagination
 */
exports.getAttendanceByMonth = async (data, queries) => {
    try {
        // Parse date range
        let { startDate, endDate } = data;
        [startDate, endDate] = [new Date(startDate), new Date(endDate)];

        // Set pagination parameters
        const limit = queries.limit ?? 20;
        const page = queries.page ?? 1;
        const sort = queries.sort ?? { createdAt: -1 };

        // Create aggregation pipeline
        const aggregate = [
            {
                $match: {
                    $and: [
                        { checkIn: { $gte: startDate } },
                        { checkIn: { $lte: endDate } }
                    ]
                }
            }
        ];

        // Execute aggregation
        const result = await Attendance.aggregate(aggregate);
        const counts = result.length;

        // Return data with pagination
        return {
            data: result,
            pagination: {
                perPage: limit,
                currentPage: page,
                counts,
                pages: Math.ceil(counts / limit)
            }
        };
    } catch (error) {
        throw error;
    }
}


exports.deleteCheckOut = async (id) => {
  const doc = await Attendance.findById(id);
  if (!doc) throw new Error("Attendance document not found");

  const lunchInTime = new Date(doc.checkOut);
  const lunchOutTime = new Date();

  const updated = await Attendance.findByIdAndUpdate(
    id,
    {
      $push: {
        lunchIn: lunchInTime,
        lunchOut: lunchOutTime
      },
      $unset: {
        checkOut: ""
      }
    },
    { new: true }
  );

  return updated;
};

