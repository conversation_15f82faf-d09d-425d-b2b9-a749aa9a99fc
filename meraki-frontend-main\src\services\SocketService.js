import io from 'socket.io-client';

let socket = null;

export const initSocket = () => {
  if (!socket) {
    socket = io('http://localhost:10000', {
      transports: ['websocket'],
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    socket.on('connect', () => {
      console.log('Web Socket connected:', socket.id);
    });

    let lastUpdateTime = 0;
    socket.on('timer-update', (timerState) => {
      const now = Date.now();
      if (now - lastUpdateTime < 500) { return } // Throttle updates
      lastUpdateTime = now;
      
      if (timerState && timerState.runningTask) {
        const newState = {
          elapsedTime: timerState.elapsedTime || 0,
          runningTask: {
            ...timerState.runningTask,
            isRunning: !timerState.runningTask.isPaused
          },
          isRunning: timerState.isRunning
        };
        
        window.dispatchEvent(new CustomEvent('timerStateChanged', { 
          detail: newState 
        }));
      } else if (timerState === null) {
        window.dispatchEvent(new CustomEvent('timerStateChanged', { 
          detail: null 
        }));
      }
    });

    socket.on('task-started', () => {
      console.log('Web received task-started');
    });

    socket.on('task-paused', () => {
      console.log('Web received task-paused');
    });

    socket.on('task-stopped', () => {
      console.log('Web received task-stopped');
    });
  }
  
  return socket;
};

export const emitTimerUpdate = (timerState) => {
  if (socket && socket.connected) {
    console.log('Web emitting timer-sync:', timerState);
    socket.emit('timer-sync', timerState);
  }
};

export const emitTaskStarted = (taskData) => {
  if (socket && socket.connected) {
    socket.emit('task-started', taskData);
  }
};

export const emitTaskPaused = (taskData) => {
  if (socket && socket.connected) {
    socket.emit('task-paused', taskData);
  }
};

export const emitTaskStopped = (taskData) => {
  if (socket && socket.connected) {
    socket.emit('task-stopped', taskData);
  }
};